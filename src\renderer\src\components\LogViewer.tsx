export default function LogViewer(): React.JSX.Element {
  return (
    <div className="flex flex-col w-full bg-gray-800/80 p-2 px-3 rounded-sm shadow-md">
      <h3 className="text-white text-sm mb-2 font-bold">📜 Log Viewer</h3>
      <div className="flex flex-col gap-[0.15rem] bg-gray-900 h-[11.625rem] rounded-sm w-full custom-scrollbar overflow-y-auto">
        <div className="flex p-1 shadow-md">
          <div className="text-white text-sm">Log messages will appear here</div>
        </div>
        <div className="flex p-1 shadow-md">
          <div className="text-white text-sm">Log messages will appear here</div>
        </div>
        <div className="flex p-1 shadow-md">
          <div className="text-white text-sm">Log messages will appear here</div>
        </div>
        <div className="flex p-1 shadow-md">
          <div className="text-white text-sm">Log messages will appear here</div>
        </div>
        <div className="flex p-1 shadow-md">
          <div className="text-white text-sm">Log messages will appear here</div>
        </div>
        <div className="flex p-1 shadow-md">
          <div className="text-white text-sm">Log messages will appear here</div>
        </div>
        <div className="flex p-1 shadow-md">
          <div className="text-white text-sm">Log messages will appear here</div>
        </div>
        <div className="flex p-1 shadow-md">
          <div className="text-white text-sm">Log messages will appear here</div>
        </div>
        <div className="flex p-1 shadow-md">
          <div className="text-white text-sm">Log messages will appear here</div>
        </div>
        <div className="flex p-1 shadow-md">
          <div className="text-white text-sm">Log messages will appear here</div>
        </div>
        <div className="flex p-1 shadow-md">
          <div className="text-white text-sm">Log messages will appear here</div>
        </div>
        <div className="flex p-1 shadow-md">
          <div className="text-white text-sm">Log messages will appear here</div>
        </div>
      </div>
    </div>
  )
}

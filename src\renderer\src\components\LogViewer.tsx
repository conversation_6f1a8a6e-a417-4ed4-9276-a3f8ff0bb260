export default function LogViewer(): React.JSX.Element {
  return (
    <div className="flex flex-col w-full bg-gray-800/80 p-1 px-2 sm:p-2 sm:px-3 rounded-sm shadow-md">
      <h3 className="text-white text-xs sm:text-sm mb-1 font-bold">📜 Log Viewer</h3>
      <div className="flex flex-col gap-[0.05rem] bg-gray-900 h-[9rem] sm:h-[10rem] md:h-[11rem] rounded-sm w-full custom-scrollbar overflow-y-auto">
        {/* Info Log Entry */}
        <div className="flex items-start px-2 py-0.5 sm:px-3 sm:py-1 hover:bg-gray-800/50 transition-colors">
          <span className="text-blue-400 text-xs mr-1 sm:mr-2 font-mono">ℹ</span>
          <div className="flex-1">
            <span className="text-gray-400 text-[10px] sm:text-xs mr-1 sm:mr-2 font-mono">
              12:34:56
            </span>
            <span className="text-white text-xs leading-tight sm:leading-normal">
              Bot connected successfully
            </span>
          </div>
        </div>

        {/* Success Log Entry */}
        <div className="flex items-start px-2 py-0.5 sm:px-3 sm:py-1 hover:bg-gray-800/50 transition-colors">
          <span className="text-green-400 text-xs mr-1 sm:mr-2 font-mono">✓</span>
          <div className="flex-1">
            <span className="text-gray-400 text-[10px] sm:text-xs mr-1 sm:mr-2 font-mono">
              12:35:12
            </span>
            <span className="text-green-300 text-xs leading-tight sm:leading-normal">
              Trade executed: BUY $25.00
            </span>
          </div>
        </div>

        {/* Warning Log Entry */}
        <div className="flex items-start px-2 py-0.5 sm:px-3 sm:py-1 hover:bg-gray-800/50 transition-colors">
          <span className="text-yellow-400 text-xs mr-1 sm:mr-2 font-mono">⚠</span>
          <div className="flex-1">
            <span className="text-gray-400 text-[10px] sm:text-xs mr-1 sm:mr-2 font-mono">
              12:35:45
            </span>
            <span className="text-yellow-300 text-xs leading-tight sm:leading-normal">
              Low balance warning: $75.00 remaining
            </span>
          </div>
        </div>

        {/* Error Log Entry */}
        <div className="flex items-start px-2 py-0.5 sm:px-3 sm:py-1 hover:bg-gray-800/50 transition-colors">
          <span className="text-red-400 text-xs mr-1 sm:mr-2 font-mono">✗</span>
          <div className="flex-1">
            <span className="text-gray-400 text-[10px] sm:text-xs mr-1 sm:mr-2 font-mono">
              12:36:01
            </span>
            <span className="text-red-300 text-xs leading-tight sm:leading-normal">
              Connection timeout - retrying...
            </span>
          </div>
        </div>

        {/* Debug Log Entry */}
        <div className="flex items-start px-2 py-0.5 hover:bg-gray-800/50 transition-colors">
          <span className="text-purple-400 text-xs mr-2 font-mono">🔧</span>
          <div className="flex-1">
            <span className="text-gray-400 text-xs mr-2 font-mono">12:36:15</span>
            <span className="text-gray-300 text-xs leading-tight">
              Strategy evaluation: confidence 0.85
            </span>
          </div>
        </div>

        {/* Trade Result Log Entry */}
        <div className="flex items-start px-2 py-0.5 hover:bg-gray-800/50 transition-colors">
          <span className="text-green-400 text-xs mr-2 font-mono">💰</span>
          <div className="flex-1">
            <span className="text-gray-400 text-xs mr-2 font-mono">12:37:30</span>
            <span className="text-green-300 text-xs leading-tight">Trade won: +$21.25 profit</span>
          </div>
        </div>

        {/* System Log Entry */}
        <div className="flex items-start px-2 py-0.5 hover:bg-gray-800/50 transition-colors">
          <span className="text-gray-400 text-xs mr-2 font-mono">⚙</span>
          <div className="flex-1">
            <span className="text-gray-400 text-xs mr-2 font-mono">12:38:00</span>
            <span className="text-gray-300 text-xs leading-tight">
              Money management: next trade $26.25
            </span>
          </div>
        </div>

        {/* Additional entries for scrolling demonstration */}
        <div className="flex items-start px-2 py-0.5 hover:bg-gray-800/50 transition-colors">
          <span className="text-blue-400 text-xs mr-2 font-mono">ℹ</span>
          <div className="flex-1">
            <span className="text-gray-400 text-xs mr-2 font-mono">12:38:30</span>
            <span className="text-white text-xs leading-tight">Market analysis complete</span>
          </div>
        </div>

        <div className="flex items-start px-2 py-0.5 hover:bg-gray-800/50 transition-colors">
          <span className="text-yellow-400 text-xs mr-2 font-mono">⚠</span>
          <div className="flex-1">
            <span className="text-gray-400 text-xs mr-2 font-mono">12:39:15</span>
            <span className="text-yellow-300 text-xs leading-tight">High volatility detected</span>
          </div>
        </div>

        <div className="flex items-start px-2 py-0.5 hover:bg-gray-800/50 transition-colors">
          <span className="text-green-400 text-xs mr-2 font-mono">✓</span>
          <div className="flex-1">
            <span className="text-gray-400 text-xs mr-2 font-mono">12:40:00</span>
            <span className="text-green-300 text-xs leading-tight">
              Signal confirmed: SELL opportunity
            </span>
          </div>
        </div>
      </div>
    </div>
  )
}
